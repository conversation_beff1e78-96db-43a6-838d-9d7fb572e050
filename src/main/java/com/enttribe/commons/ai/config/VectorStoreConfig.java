package com.enttribe.commons.ai.config;

import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import com.enttribe.commons.ai.util.JsonUtils;
import com.enttribe.commons.ai.util.ModelOptionsUtils;
import com.enttribe.commons.ai.util.VectorUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.observation.ObservationRegistry;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.observation.VectorStoreObservationConvention;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.ai.vectorstore.redis.autoconfigure.RedisVectorStoreProperties;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;


import org.springframework.data.redis.connection.RedisNode;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import redis.clients.jedis.DefaultJedisClientConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisClientConfig;
import redis.clients.jedis.JedisPooled;
import redis.clients.jedis.JedisSentinelPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;


import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.time.Duration;
import java.util.stream.Collectors;

/**
 * Configuration class responsible for managing and initializing Vector Store instances in the application.
 * This class handles the setup and configuration of vector stores, particularly focusing on Milvus vector
 * store implementation. It provides functionality to create, manage, and retrieve vector store instances
 * based on configuration parameters.
 *
 * <p>The class reads vector store configurations from JSON properties and supports dynamic initialization
 * of multiple vector store instances. It primarily works with Milvus vector stores but is designed to be
 * extensible for other vector store implementations.
 *
 * <p>Key features:
 * <ul>
 *     <li>Dynamic vector store initialization from JSON configuration</li>
 *     <li>Support for Milvus vector store with customizable parameters</li>
 *     <li>Thread-safe vector store instance management</li>
 *     <li>Integration with Spring AI's embedding models and observation registry</li>
 * </ul>
 *
 * <AUTHOR>
 * @see VectorStore
 * @see InferenceManager
 */
@Component
@EnableConfigurationProperties({ RedisVectorStoreProperties.class, RedisProperties.class})
public class VectorStoreConfig {

    /**
     * JSON configuration property key for vector store settings.
     */
    private static String VECTOR_STORE_CONFIG_JSON = "W10=";//base 64 encoded []

    /**
     * Thread-safe map storing vector store instances with their corresponding keys.
     */
    private static volatile Map<String, VectorStore> vectorStoreMap = new HashMap<>();

    private static final Logger log = LoggerFactory.getLogger(VectorStoreConfig.class);

    private final RedisVectorStoreProperties redisVectorStoreProperties;
    private final RedisProperties redisProperties;
    private final InferenceManager inferenceManager;
    private final BatchingStrategy batchingStrategy;
    private final ObjectProvider<ObservationRegistry> observationRegistry;
    private final ObjectProvider<VectorStoreObservationConvention> customObservationConvention;
    private static Map<String, Map<String, Object>> vectorIdMap = new HashMap<>();

    @Value("${commons.ai.sdk.redis.trustStorePath:}")
    private String trustStorePath;

    @Value("${commons.ai.sdk.redis.trustStorePassword:}")
    private String trustStorePassword;

    @Value("${commons.ai.sdk.keyStoreInstance:jks}")
    private String keyStoreInstance;

    @Value("${commons.ai.sdk.redis.sentinel.password:}")
    private String sentinelPassword;
    /**
     * Constructs a new VectorStoreConfig with the specified dependencies.
     *
     * @param inferenceManager              Manager for handling inference models
     * @param batchingStrategy              Strategy for batching operations
     * @param observationRegistry           Registry for observations and metrics
     * @param customObservationConvention   Custom convention for vector store observations
     */
    public VectorStoreConfig(InferenceManager inferenceManager, BatchingStrategy batchingStrategy,
                             ObjectProvider<ObservationRegistry> observationRegistry, ObjectProvider<VectorStoreObservationConvention> customObservationConvention,
                             @Value("${commons.ai.sdk.vector_store.config:W10=}") String vectorStoreConfigJson, RedisVectorStoreProperties redisVectorStoreProperties, RedisProperties redisProperties) {
        this.inferenceManager = inferenceManager;
        this.batchingStrategy = batchingStrategy;
        this.observationRegistry = observationRegistry;
        this.customObservationConvention = customObservationConvention;
        this.redisVectorStoreProperties = redisVectorStoreProperties;
        this.redisProperties = redisProperties;
        VECTOR_STORE_CONFIG_JSON = new String(Base64.getDecoder().decode(vectorStoreConfigJson));
    }

    /**
     * Parses the vector store configuration from JSON.
     *
     * @return List of configuration maps for vector stores
     */
    private List<Map<String, Object>> parseVectorStoreConfigJson() {
        try {
            ObjectMapper objectMapper = JsonUtils.getObjectMapper();
            return objectMapper.readValue(VectorStoreConfig.VECTOR_STORE_CONFIG_JSON, new TypeReference<List<Map<String, Object>>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("error while parsing commons.ai.sdk.vector_store.config", e);
            return List.of();
        }
    }

    /**
     * Initializes vector store instances based on the configuration.
     * This method is called automatically after bean construction.
     */
    @PostConstruct
    public void init() {

        log.info("vectorStore instances are (before) : {}", vectorStoreMap.keySet());
        List<Map<String, Object>> vectorStoreConfigList = parseVectorStoreConfigJson();

        Map<String, VectorStore> vectorStoreTempMap = new HashMap<>();
        for (Map<String, Object> vectorStoreConfig : vectorStoreConfigList) {
            String vectorDatabase = (String) vectorStoreConfig.get("vectorDatabase");
            VectorUtils.validateVectorDatabaseName(vectorDatabase);
            if ("redis".equals(vectorDatabase)) {
                VectorStore redisVectorStore = getRedisVectorStoreInstance(vectorStoreConfig);
                String vectorStoreKey = VectorUtils.generateVectorStoreKey(vectorStoreConfig);
                vectorStoreTempMap.put(vectorStoreKey, redisVectorStore);
            }
        }

        if (!vectorStoreTempMap.isEmpty()) {
            log.info("updating vector store map");
            VectorStoreConfig.vectorStoreMap = Map.copyOf(vectorStoreTempMap);
            log.info("vector store map updated successfully");
        } else {
            log.warn("vector store map is empty");
        }
        log.info("vectorStore instances are (after) : {}", vectorStoreMap.keySet());

        VectorStoreConfig.vectorIdMap = prepareVectorIdMap(vectorStoreConfigList);
        log.info("vectorIdMap is prepared for keys : {}", vectorIdMap.keySet());
    }

    private VectorStore getRedisVectorStoreInstance(Map<String, Object> vectorStoreConfig) {
        RedisProperties redisProperties1 = ModelOptionsUtils.merge(vectorStoreConfig, redisProperties, RedisProperties.class);
        RedisVectorStoreProperties redisVectorStoreProperties1 = ModelOptionsUtils.merge(vectorStoreConfig, redisVectorStoreProperties, RedisVectorStoreProperties.class);

        JedisPooled jedisPooled = null;
        try {
            jedisPooled = createJedisPooledWithSentinel(redisProperties1);
        } catch (Exception e) {
            log.error("error in creating jedis sentinel connection : {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }

        String inference = (String) vectorStoreConfig.get("inference");
        String model = (String) vectorStoreConfig.get("embeddingModel");
        String key = String.format("%s_%s", inference, model);
        EmbeddingModel embeddingModel = inferenceManager.getEmbeddingModel(key);
        Assert.notNull(embeddingModel, String.format("embedding model is null for key %s", key));

        List<RedisVectorStore.MetadataField> metadataFieldList = new ArrayList<>();
        if (vectorStoreConfig.containsKey("metadataFields")) {
            List<String> metadataFields = (List<String>) vectorStoreConfig.get("metadataFields");
            String json = JsonUtils.convertToJSON(metadataFields);
            try {
                metadataFieldList = JsonUtils.convertJsonToList(json, RedisVectorStore.MetadataField.class);
            } catch (JsonProcessingException e) {
                log.error("error in parsing metadataFields : {}", metadataFields, e);
                throw new RuntimeException(e);
            }
        }

        RedisVectorStore redisVectorStore = RedisVectorStore.builder(jedisPooled, embeddingModel)
                .initializeSchema(redisVectorStoreProperties1.isInitializeSchema())
                .observationRegistry(observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP))
                .customObservationConvention(customObservationConvention.getIfAvailable(() -> null))
                .batchingStrategy(batchingStrategy)
                .vectorAlgorithm(RedisVectorStore.Algorithm.FLAT)
                .metadataFields(metadataFieldList)
                .indexName(redisVectorStoreProperties1.getIndexName())
                .prefix(redisVectorStoreProperties1.getPrefix())
                .build();
        redisVectorStore.afterPropertiesSet();
        return redisVectorStore;
    }

    private JedisPooled createJedisPooledWithSentinel(RedisProperties redisProperties) throws Exception {

        String masterName = redisProperties.getSentinel().getMaster();

        // Convert nodes to HostAndPort set
        Set<HostAndPort> sentinelNodes = redisProperties.getSentinel().getNodes().stream()
                .map(HostAndPort::from)
                .collect(Collectors.toSet());

        log.info("Configuring Redis Sentinel connection. Master: {}, Sentinels: {}", masterName, sentinelNodes);

        // Pool config
        GenericObjectPoolConfig<Jedis> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setBlockWhenExhausted(true);
        poolConfig.setMaxWait(Duration.ofSeconds(10));
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofSeconds(30));
        poolConfig.setMinEvictableIdleDuration(Duration.ofMinutes(1));
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(false);

        // --- Master client config ---
        DefaultJedisClientConfig.Builder masterConfigBuilder = DefaultJedisClientConfig.builder()
                .connectionTimeoutMillis(5000)
                .socketTimeoutMillis(3000)
                .password(redisProperties.getPassword())
                .database(redisProperties.getDatabase());

        if (redisProperties.getSsl().isEnabled()) {
            log.info("Setting up SSL for Redis master connection");
            SSLSocketFactory sslSocketFactory = createSSLSocketFactory();
            masterConfigBuilder.ssl(true)
                    .sslSocketFactory(sslSocketFactory)
                    .hostnameVerifier((hostname, session) -> true);
        }
        JedisClientConfig masterClientConfig = masterConfigBuilder.build();

        // --- Sentinel client config ---
        DefaultJedisClientConfig.Builder sentinelConfigBuilder = DefaultJedisClientConfig.builder()
                .connectionTimeoutMillis(5000)
                .socketTimeoutMillis(3000)
                .password(redisProperties.getPassword()); // required for "requirepass" in Sentinel

        if (redisProperties.getSsl().isEnabled()) {
            log.info("Setting up SSL for Sentinel connection");
            SSLSocketFactory sslSocketFactory = createSSLSocketFactory();
            sentinelConfigBuilder
                    .ssl(true)
                    .sslSocketFactory(sslSocketFactory)
                    .hostnameVerifier((hostname, session) -> true);
        }
        JedisClientConfig sentinelClientConfig = sentinelConfigBuilder.build();

        // Create Sentinel pool with proper configs
        JedisSentinelPool sentinelPool = new JedisSentinelPool(
                masterName,
                sentinelNodes,
                poolConfig,
                masterClientConfig,
                sentinelClientConfig
        );

        // Get the current master address
        HostAndPort currentMaster = sentinelPool.getCurrentHostMaster();
        log.info("Redis Sentinel resolved master to: {}", currentMaster);

        // Create JedisPooled using resolved master
        return new JedisPooled(currentMaster, masterClientConfig);
    }

    private SSLSocketFactory createSSLSocketFactory() throws Exception {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");

        X509Certificate caCert;
        try (FileInputStream fis = new FileInputStream(trustStorePath)) {
            caCert = (X509Certificate) cf.generateCertificate(fis);
        }

        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("redis8-ca", caCert);

        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);

        return sslContext.getSocketFactory();
    }

    private Map<String, Map<String, Object>> prepareVectorIdMap(List<Map<String, Object>> vectorStoreConfigList) {
        Map<String, Map<String, Object>> vectorIdMap = new HashMap<>();
        for (Map<String, Object> vectorStoreConfig : vectorStoreConfigList) {
            String vectorStoreId = (String) vectorStoreConfig.get("vector_store_id");
            if (vectorStoreId == null || vectorStoreId.isEmpty()) {
                log.warn("there is no vector_store_id property in object of commons.ai.sdk.vector_store.config. consider adding one");
                continue;
            }
            vectorIdMap.put(vectorStoreId, vectorStoreConfig);
        }
        return vectorIdMap;
    }

    /**
     * Retrieves a vector store instance based on the provided metadata.
     *
     * @param metaData Metadata containing information to identify the vector store
     * @return The corresponding vector store instance
     * @throws IllegalArgumentException if metaData is null
     */
    public VectorStore getVectorStore(VectorMetaData metaData) {
        Assert.notNull(metaData, "vector meta data must not be null");
        log.debug("vectorStoreMap keys are : {} and vectorMetaData : {}", vectorStoreMap.keySet(), metaData);
        String vectorStoreKey = VectorUtils.generateVectorStoreKey(metaData);
        if (vectorStoreMap.containsKey(vectorStoreKey)) {
            return vectorStoreMap.get(vectorStoreKey);
        } else {
            log.warn("vector store map does not contain instance for key : {}", vectorStoreKey);
            if (VECTOR_STORE_CONFIG_JSON.equals("[]") || VECTOR_STORE_CONFIG_JSON.equals("W10=")) {
                log.warn("property : 'commons.ai.sdk.vector_store.config' is not declared in your application.properties. You must declared the property to use vector store");
            } else {
                log.warn("no vector store found for key : {}", vectorStoreKey);
            }
            VectorStore vectorStore = vectorStoreMap.get(vectorStoreKey);
            Assert.notNull(vectorStore, String.format("vector store must not be null. vectorStoreMap keys are : %s and key : %s", vectorStoreMap.keySet(), vectorStoreKey));
            return vectorStore;
        }
    }

    public VectorStore getVectorStore(String vectorStoreId) {
        Assert.hasText(vectorStoreId, "vector store id must not be null or empty");
        log.debug("vectorIdMap keys are : {} and vectorStoreId : {}", vectorIdMap.keySet(), vectorStoreId);
        Map<String, Object> vectorMetaData = vectorIdMap.get(vectorStoreId);
        String vectorStoreKey = VectorUtils.generateVectorStoreKey(vectorMetaData);
        VectorStore vectorStore = vectorStoreMap.get(vectorStoreKey);
        Assert.notNull(vectorStore, String.format("vector store is null for the vectorStoreId : %s", vectorStoreId));
        return vectorStore;
    }

}
